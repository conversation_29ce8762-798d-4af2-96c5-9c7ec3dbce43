<!-- 收款确认 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Textarea as ATextarea,
  Tooltip,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { receiptConfirmApi, fileApi } from '@haierbusiness-front/apis';
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog } from '@haierbusiness-front/composables';
import router from '../../router';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import { ConfirmStatusEnum, ConfirmStatusMap, ReceiptRecord } from '@haierbusiness-front/common-libs';
import ReceiptSelect from '@haierbusiness-front/components/mice/receipt/ReceiptSelect.vue';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '业务单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '230px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '确认用户',
    dataIndex: 'confirmUserName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '确认时间',
    dataIndex: 'confirmTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '250px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPaymentFromFilter>({});
const statusOptions = ref([
  {
    label: ConfirmStatusMap[ConfirmStatusEnum.PENDING],
    value: ConfirmStatusEnum.PENDING,
  },
  {
    label: ConfirmStatusMap[ConfirmStatusEnum.CONFIRMED],
    value: ConfirmStatusEnum.CONFIRMED,
  },
  {
    label: ConfirmStatusMap[ConfirmStatusEnum.REJECTED],
    value: ConfirmStatusEnum.REJECTED,
  },
]);

const { data, run: listApiRun, loading, current, pageSize } = usePagination(receiptConfirmApi.getPayMentList);

const reset = () => {
  searchParam.value = {};
  beginAndEnd.value = undefined;
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const detailVisible = ref(false);
const currentDetailRecord = ref<any>(null);
const detailLoading = ref(false);
const viewMode = ref<'view' | 'approve'>('view'); // 弹窗模式：查看或审批

//查看/审批
const handleView = (record?: any, mode: 'view' | 'approve' = 'view') => {
  console.log('handleView 被调用, record:', record, 'mode:', mode);
  
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true;
    viewMode.value = mode;
    // 这里可以调用详情接口获取完整数据，暂时直接使用传入的record
    currentDetailRecord.value = record;
    detailVisible.value = true;
    detailLoading.value = false;
    
    // 调用详情接口获取完整数据
    receiptConfirmApi
      .getPaymentDetails(record.id)
      .then((res) => {
        console.log('获取详情成功:', res);
        currentDetailRecord.value = res;
        detailVisible.value = true;
        if (mode === 'approve') {
          fileList.value = [];
          ReasonsRejection.value = '';
          selectedReceiptCode.value = (res as any).receivePaymentCode || '';
          showRejectReason.value = false;
        }
      })
      .catch((error) => {
        console.error('获取详情失败:', error);
        message.error('获取详情失败，请重试');
        // 即使获取详情失败，也要关闭loading
        detailVisible.value = false;
      })
      .finally(() => {
        detailLoading.value = false;
      });
  } else {
    console.log('关闭详情弹窗或record无效');
    // 关闭详情弹窗
    detailVisible.value = false;
    currentDetailRecord.value = null;
    fileList.value = [];
    ReasonsRejection.value = '';
    selectedReceiptCode.value = '';
    showRejectReason.value = false;
  }
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

// 上传付款凭证相关
const uploadLoading = ref(false);
const fileList = ref<any[]>([]);
const ReasonsRejection = ref('');
const selectedReceiptCode = ref('');
const showRejectReason = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 添加收款记录选择弹窗相关状态
const receiptSelectVisible = ref(false);

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  handleView(record, 'approve');
};

// 关闭上传弹窗（已合并到handleView函数中）

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '订单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '需求时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议经办人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  console.log('开始上传文件:', options.file.name);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      console.log('文件上传成功:', options.file);

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 确认操作
const submitConfirm = () => {
  if (!selectedReceiptCode.value) {
    message.warning('请选择收款单号');
    return;
  }

  uploadLoading.value = true;

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    uploadLoading.value = false;
    return;
  }

  receiptConfirmApi
    .confirmPaymentPayment({
      id: currentDetailRecord.value.id,
      receivePaymentCode: currentDetailRecord.value.receivePaymentCode,
      sapReceiveCode: selectedReceiptCode.value,
      remark: ReasonsRejection.value || '',
      receiveVoucherUrl: attachmentFiles,
    })
    .then(() => {
      message.success('确认成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('确认失败:', error);
      message.error('确认失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 打开选择收款记录弹窗
const openReceiptSelect = () => {
  receiptSelectVisible.value = true;
};

// 处理收款单号选择
const handleSelectReceipt = (record: ReceiptRecord) => {
  const extendedRecord = record as any;

  let sapReceiveCode = '';
  if (extendedRecord.originalData) {
    const budat = extendedRecord.originalData.budat || '';
    const belnr = extendedRecord.originalData.belnr || '';
    if (budat) {
      try {
        const date = new Date(Number(budat));
        const year = date.getFullYear();
        sapReceiveCode = `${year}${belnr}`;
      } catch (error) {
        sapReceiveCode = belnr;
      }
    } else {
      sapReceiveCode = belnr;
    }
  } else {
    sapReceiveCode = record.sapReceiveNo || '';
  }

  // 调用校验接口
  receiptConfirmApi
    .receiveIsReceive({ sapReceiveCode } as any)
    .then((res) => {
      selectedReceiptCode.value = sapReceiveCode;
      message.success('收款单号选择成功');
    })
    .catch((error) => {
      console.error('校验收款单号失败:', error);
      message.error('收款单号校验失败，请重新选择');
    });
};

// 驳回操作
const submitReject = () => {
  if (!ReasonsRejection.value.trim()) {
    message.warning('请填写驳回原因');
    return;
  }

  uploadLoading.value = true;

  receiptConfirmApi
    .rejectPayment({
      id: currentDetailRecord.value.id,
      receivePaymentCode: currentDetailRecord.value.receivePaymentCode,
      sapReceiveCode: selectedReceiptCode.value || '',
      remark: ReasonsRejection.value,
    })
    .then(() => {
      message.success('驳回成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('驳回失败:', error);
      message.error('驳回失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 处理操作按钮点击事件
const handleActionClick = (record: any, action: string) => {
  switch (action) {
    case 'view':
      handleView(record, 'view');
      break;
    case 'approve':
      handleView(record, 'approve');
      break;
    default:
      break;
  }
};
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="(searchParam as any).merchantCode" placeholder="请输入服务商" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="businessCode">收款单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="(searchParam as any).receivePaymentCode" placeholder="请输入收款单号" allow-clear />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select
              v-model:value="(searchParam as any).state"
              placeholder="请选择状态"
              allow-clear
              style="width: 100%"
            >
              <h-select-option v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'state'">
              {{ ConfirmStatusMap[record.state as keyof typeof ConfirmStatusMap] || '未知状态' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleActionClick(record, 'view')">查看</h-button>
                <h-button
                  v-if="record.state === ConfirmStatusEnum.PENDING"
                  type="link"
                  @click="handleActionClick(record, 'approve')"
                >
                  审批
                </h-button>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 付款单详情/审批弹窗 -->
    <Modal
      v-model:open="detailVisible"
      :title="viewMode === 'view' ? '财务确认详情' : '收款确认'"
      :footer="null"
      @cancel="handleView()"
      width="600px"
      :loading="detailLoading"
    >
      <div v-if="currentDetailRecord" class="receipt-confirm-modal">
        <div class="approval-single-column">
          <!-- 信息展示区域 -->
          <div class="info-section">
            <div class="info-row">
              <span class="label">业务单号：</span>
              <span class="value">{{
                currentDetailRecord.businessCode || currentDetailRecord.receivePaymentCode
              }}</span>
            </div>
            <div class="info-row">
              <span class="label">服务商名称：</span>
              <span class="value">{{ currentDetailRecord.merchantName }}</span>
            </div>
            <div class="info-row">
              <span class="label">收款金额：</span>
              <span class="value">{{ currentDetailRecord.receivePaymentAmount }}元</span>
            </div>
            <div class="info-row">
              <span class="label">业务系统：</span>
              <span class="value">{{ currentDetailRecord.businessSystem || '会展' }}</span>
            </div>
            <div class="info-row">
              <span class="label">创建时间：</span>
              <span class="value">{{ currentDetailRecord.gmtCreate }}</span>
            </div>
            <div class="info-row">
              <span class="label">付款凭证：</span>
              <span class="value">
                <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
                  <a :href="currentDetailRecord.attachmentFiles[0].path" target="_blank" style="color: #1890ff">
                    {{
                      currentDetailRecord.attachmentFiles[0].path
                        ? currentDetailRecord.attachmentFiles[0].path.split('/').pop()
                        : '付款凭证.pdf'
                    }}
                  </a>
                </template>
                <span v-else>暂无</span>
              </span>
            </div>
            <div class="info-row">
              <span class="label">收款描述：</span>
              <span class="value">{{ currentDetailRecord.description || '会务平台服务营收收款' }}</span>
            </div>
            <div class="info-row">
              <span class="label">状态：</span>
              <span class="value">{{
                ConfirmStatusMap[currentDetailRecord.state as keyof typeof ConfirmStatusMap] || '未知状态'
              }}</span>
            </div>
          </div>

          <!-- 操作区域（仅审批模式显示） -->
          <div v-if="viewMode === 'approve'" class="operation-section">
            <div class="info-row">
              <span class="label">收款单号：</span>
              <span class="value" style="display: flex; align-items: center">
                <h-input
                  v-model:value="selectedReceiptCode"
                  placeholder="请选择收款单号"
                  style="width: 200px; margin-right: 10px"
                  disabled
                />
                <h-button type="primary" size="small" @click="openReceiptSelect"> 选择 </h-button>
              </span>
            </div>

            <!-- 驳回原因输入区域 -->
            <div class="info-row">
              <span class="label">驳回原因：</span>
              <span class="value" style="width: 100%">
                <a-textarea
                  v-model:value="ReasonsRejection"
                  placeholder="请输入驳回原因"
                  :rows="4"
                  style="width: 100%"
                  :maxlength="200"
                  show-count
                />
              </span>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <!-- 查看模式按钮 -->
          <template v-if="viewMode === 'view'">
            <h-button style="margin-right: 16px" @click="handleView()">取消</h-button>
            <h-button type="primary" @click="handleView()">确定</h-button>
          </template>
          <!-- 审批模式按钮 -->
          <template v-else-if="viewMode === 'approve'">
            <h-button @click="submitReject" :loading="uploadLoading" class="reject-btn"> 驳回 </h-button>
            <h-button type="primary" @click="submitConfirm" :loading="uploadLoading" class="confirm-btn">
              确定
            </h-button>
          </template>
        </div>
      </div>
    </Modal>

    <!-- 使用封装的收款记录选择组件 -->
    <ReceiptSelect
      :visible="receiptSelectVisible"
      @update:visible="receiptSelectVisible = $event"
      @select="handleSelectReceipt"
      @cancel="receiptSelectVisible = false"
    />
  </div>
</template>

<style scoped lang="less">
.main-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-standard {
  padding: 10px 10px 0px 10px;
}

.text-right-padding {
  text-align: right;
  padding-right: 10px;
}

.width-full {
  width: 100%;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-right-10 {
  margin-right: 10px;
}

.modal-padding {
  padding: 20px 0;
}

.info-item {
  margin-bottom: 16px;
}

.info-item-12 {
  margin-bottom: 12px;
}

.upload-section {
  margin: 16px 0;
  display: flex;
}

.textarea-section {
  margin-bottom: 16px;
  display: flex;
}

.footer-buttons {
  text-align: right;
  margin-top: 20px;
}

.table-margin {
  margin-top: 15px;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}

// 新增审批弹窗样式
.receipt-confirm-modal {
  .approval-single-column {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .info-section {
      .info-row {
        display: flex;
        margin-bottom: 16px;
        align-items: center;

        .label {
          min-width: 100px;
          color: #666;
          font-size: 14px;
          text-align: right;
          margin-right: 12px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;
          display: flex;
          align-items: center;
        }
      }
    }

    .operation-section {
      .info-row {
        display: flex;
        margin-bottom: 16px;
        align-items: center;

        .label {
          min-width: 100px;
          color: #666;
          font-size: 14px;
          text-align: right;
          margin-right: 12px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .modal-footer {
    text-align: right;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;

    .reject-btn {
      margin-right: 16px;
      padding: 6px 24px;
      height: 36px;
    }

    .confirm-btn {
      padding: 6px 24px;
      height: 36px;
    }
  }
}
</style>
