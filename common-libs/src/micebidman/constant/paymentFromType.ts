// 付款单状态枚举
export enum PaymentFromStatusEnum {
    PENDING_PAYMENT_UPLOAD = 10,      // 待上传支付凭证
    PENDING_FINANCIAL_CONFIRM = 20,  // 待财务确认收款
    PAYMENT_REJECTED = 30,           // 收款驳回
    PENDING_INVOICE_UPLOAD = 40,     // 待国旅上传发票
    APPROVAL_REJECTED = 50,           // 审批驳回
    APPROVING = 60,                  // 审批中
    COMPLETED = 70,                  // 已完成
  }

// 发票状态枚举
export enum InvoiceStatusEnum {
    PENDING_SERVICE_PROVIDER_UPLOAD = 10,  // 待服务商上传发票
    PENDING_FINANCIAL_CONFIRM = 20,        // 待财务确认收款
    FINANCIAL_REJECTED = 30,               // 财务驳回
    COMPLETED = 40,                        // 已完成
  }
  
  export const PaymentFromStatusMap = {
    [PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD]: '待上传支付凭证',
    [PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM]: '待财务确认收款',
    [PaymentFromStatusEnum.PAYMENT_REJECTED]: '收款驳回',
    [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: '待国旅上传发票',
    [PaymentFromStatusEnum.APPROVAL_REJECTED]: '审批驳回',
    [PaymentFromStatusEnum.APPROVING]: '审批中',
    [PaymentFromStatusEnum.COMPLETED]: '已完成',
  } as const;

export const InvoiceStatusMap = {
    [InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD]: '待服务商上传发票',
    [InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM]: '待财务确认收款',
    [InvoiceStatusEnum.FINANCIAL_REJECTED]: '财务驳回',
    [InvoiceStatusEnum.COMPLETED]: '已完成',
  } as const;

export const PaymentFromStatusTagColorMap = {
  [PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD]: 'orange',
  [PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM]: 'blue',
  [PaymentFromStatusEnum.PAYMENT_REJECTED]: 'red',
  [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: 'purple',
  [PaymentFromStatusEnum.APPROVAL_REJECTED]: 'red',
  [PaymentFromStatusEnum.APPROVING]: 'processing',
  [PaymentFromStatusEnum.COMPLETED]: 'green',
} as const;

export const InvoiceStatusTagColorMap = {
  [InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD]: 'orange',
  [InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM]: 'blue',
  [InvoiceStatusEnum.FINANCIAL_REJECTED]: 'red',
  [InvoiceStatusEnum.COMPLETED]: 'green',
} as const;

// 确认状态枚举
export enum ConfirmStatusEnum {
  PENDING = 0,    // 待确认
  CONFIRMED = 1,  // 已确认
  REJECTED = 2,   // 驳回
}

export const ConfirmStatusMap = {
  [ConfirmStatusEnum.PENDING]: '待确认',
  [ConfirmStatusEnum.CONFIRMED]: '已确认',
  [ConfirmStatusEnum.REJECTED]: '驳回',
} as const;

// 文件类型枚举
export enum FileTypeEnum {
  REFUND_VOUCHER_FINANCE = 160,           // 退款凭证（财务上传）
  PAYMENT_VOUCHER_FINANCE = 161,          // 支付凭证（财务上传）
  PAYMENT_VOUCHER_SERVICE_PROVIDER = 162,  // 支付凭证（服务商上传）
  RECEIPT_VOUCHER_FINANCE = 163,          // 收款凭证（财务上传）
  PAYMENT_VOUCHER_SERVICE_PROVIDER_2 = 164, // 支付凭证（服务商上传）
  PAYMENT_VOUCHER_FINANCE_2 = 165,        // 付款凭证（财务上传）
  ACCOMMODATION_DETAIL_ATTACHMENT = 560,   // 住宿详单附件
  EXCHANGE_RATE_EVIDENCE = 570,           // 汇率见证性材料
  INSURANCE_ATTACHMENT = 580,              // 保单附件
  SETTLEMENT_ATTACHMENT = 590,             // 结算单附件
}

// 文件类型映射
export const FileTypeMap = {
  [FileTypeEnum.REFUND_VOUCHER_FINANCE]: '退款凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_FINANCE]: '支付凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER]: '支付凭证（服务商上传）',
  [FileTypeEnum.RECEIPT_VOUCHER_FINANCE]: '收款凭证（财务上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER_2]: '支付凭证（服务商上传）',
  [FileTypeEnum.PAYMENT_VOUCHER_FINANCE_2]: '付款凭证（财务上传）',
  [FileTypeEnum.ACCOMMODATION_DETAIL_ATTACHMENT]: '住宿详单附件',
  [FileTypeEnum.EXCHANGE_RATE_EVIDENCE]: '汇率见证性材料',
  [FileTypeEnum.INSURANCE_ATTACHMENT]: '保单附件',
  [FileTypeEnum.SETTLEMENT_ATTACHMENT]: '结算单附件',
} as const;
  
    